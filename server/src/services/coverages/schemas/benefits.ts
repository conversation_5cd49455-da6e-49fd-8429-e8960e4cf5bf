import {Type, ObjectIdSchema } from '@feathersjs/typebox';
import { premiumSchema } from './rates.js'

import { coins_categories, benSchema } from '../../marketplace/utils/index.js'

const dedSchema = Type.Object({
    name: Type.String({ $comment: 'deductible name (ie: family major medical)' }),
    waivable: Type.Boolean({ $comment: 'waive-able for a preferred network/behavior' }),
    detail: Type.String({ $comment: 'descriptive detail' }),
    cats: Type.Array(ObjectIdSchema(), { $comment: 'to be added later by a user' }),
    single: Type.Number({ $comment: 'single amount' }),
    family: Type.Optional(Type.Number({ $comment: 'family amount - do not add 0, do not include if not applicable' })),
    type: Type.String({ enum: ['event', 'annual'], $comment: 'whether this is per event or per year' })
}, { description: 'Deductible structure for a coverage' });

const dedsSchema = Type.Record(
    Type.String({ pattern: '^.*$' }),
    dedSchema,
    {
        description: 'any key with dedSchema as the details of the deductible'
    }
);


const coinsSchema = Type.Object({
    name: Type.String({ $comment: 'coinsurance name (ie: emergency room)' }),
    detail: Type.String({ $comment: 'descriptive detail' }),
    cats: Type.Array(ObjectIdSchema(), { $comment: 'to be added later by a user' }),
    amount: Type.Number(),
    category: Type.String({ enum: coins_categories })
}, { description: 'Coinsurance structure for a coverage' });

const coins_s_schema = Type.Record(
    Type.String({ pattern: '^.*$' }),
    coinsSchema,
    {
        description: 'any key with coinsSchema as the details of the coinsurance'
    }
);
const coverageTypeEnum = ['mm', 'mec', 'hs', 'dc', 'eb', 'hra']
const baseCoverageSchema = Type.Object({
    carrierName: Type.String({ $comment: 'name of the insurance company' }),
    webpage: Type.String({ $comment: 'link to details webpage if available' }),
    name: Type.String({ $comment: 'name of the coverage' }),
    openNetwork: Type.Boolean(),
    plan_type: Type.String({ $comment: 'network type such as HMO, PPO, EPO, POS' }),
    type: Type.String({ enum: coverageTypeEnum, $comment: 'major medical, health share, direct care, excepted benefit' }),
    description: Type.String({ $comment: 'brief coverage description' }),
    hsaQualified: Type.Boolean({ $comment: 'high deductible health plan - eligible for HSA contributions' }),
    productDetailRef: Type.String({ $comment: 'For health shares - this is the health share `_id:product_id`' }),
    fortyPremium: Type.Number(),
    maxAge: Type.Number(),
    preventive: Type.Boolean(),
    coinsurance: coinsSchema, //what the participant pays
    coins: coins_s_schema,
    deductible: dedSchema,
    deductibles: dedsSchema,
    cap: dedSchema,
    caps: dedsSchema,
    copays: coins_s_schema,
    premium: premiumSchema,
    benefits: benSchema,
    rates: Type.Array(ObjectIdSchema(), { $comment: 'special rate areas - to be added by a user later' }),
    deductibleType: Type.String({ enum: ['annual', 'event'], $comment: 'deprecated - now see deductible.type' }),
    moop: dedSchema,
    moops: Type.Record(
        Type.String({ pattern: '^.*$' }),
        dedSchema,
        {
            description: 'any key with dedSchema as the details of the moop'
        }
    ),
    monthsSinceSmoked: Type.Number({ $comment: 'at how many months someone is considered non-tobacco user' }),
    catsBlacklist: Type.Record(
        Type.String({ pattern: '^.*$' }),
        Type.Object({
            id: Type.Union([ObjectIdSchema(), Type.Object({}, { additionalProperties: true })]),
            memo: Type.String()
        }),
        {
            description: 'any key with an object with id and memo as the details of the blacklist'
        }
    )
}, { additionalProperties: false })

export const coverageCalcSchema = Type.Object({
    ...baseCoverageSchema.properties,
    covered: Type.String({ enum: ['individual', 'group']}),
}, { additionalProperties: false })
