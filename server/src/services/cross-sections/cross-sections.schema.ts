// TypeBox schema for cross-sections service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, commonPatch, queryWrapper } from '../../utils/common/typebox-schemas.js'

export const crossSectionsSchema = Type.Object({
  _id: ObjectIdSchema(),
  hackId: Type.String(),
  subject: ObjectIdSchema(),
  sections: Type.Record(Type.String(), Type.Array(ObjectIdSchema())),
  ...commonFields
}, { $id: 'CrossSections', additionalProperties: false })

export type CrossSections = Static<typeof crossSectionsSchema>
export const crossSectionsValidator = getValidator(crossSectionsSchema, dataValidator)
export const crossSectionsResolver = resolve<CrossSections, HookContext>({})
export const crossSectionsExternalResolver = resolve<CrossSections, HookContext>({})

export const crossSectionsDataSchema = Type.Object({
  ...Type.Omit(crossSectionsSchema, ['_id']).properties
}, { additionalProperties: false })

export type CrossSectionsData = Static<typeof crossSectionsDataSchema>
export const crossSectionsDataValidator = getValidator(crossSectionsDataSchema, dataValidator)
export const crossSectionsDataResolver = resolve<CrossSectionsData, HookContext>({})

// Pick ObjectId fields and nested ObjectId fields for query properties
const crossSectionsQueryProperties = Type.Pick(crossSectionsSchema, ['_id', 'subject', 'sections'])

export const crossSectionsPatchSchema = commonPatch(crossSectionsSchema, { pushPullOpts: [], pickedForSet: crossSectionsQueryProperties })
export type CrossSectionsPatch = Static<typeof crossSectionsPatchSchema>
export const crossSectionsPatchValidator = getValidator(crossSectionsPatchSchema, dataValidator)
export const crossSectionsPatchResolver = resolve<CrossSectionsPatch, HookContext>({})

export const crossSectionsQuerySchema = queryWrapper(crossSectionsSchema)
export type CrossSectionsQuery = Static<typeof crossSectionsQuerySchema>
export const crossSectionsQueryValidator = getValidator(crossSectionsQuerySchema, queryValidator)
export const crossSectionsQueryResolver = resolve<CrossSectionsQuery, HookContext>({})
